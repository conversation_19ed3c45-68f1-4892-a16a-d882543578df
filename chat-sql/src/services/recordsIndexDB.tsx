'use client'

export type LLMProblem = {
  id?: number; // IndexedDB自动生成的键
  data: any; // 直接存储整个LLM返回的数据
  createdAt: Date; // 必需字段
  title?: string; // 自定义标题
  isFavorite?: boolean; // 是否收藏
  isTutorial?: boolean; // 是否为教程
  progress?: number; // 用户完成的问题数量
  totalProblems?: number; // 该记录总共包含的问题数量
  completedProblems?: boolean[]; // 每个问题的完成状态数组
};

const DB_NAME = 'llm_problems_db';
const DB_VERSION = 1;
const STORE_NAME = 'problems';

// 初始化数据库
export const initDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = () => {
      reject('IndexedDB 打开失败');
    };

    request.onsuccess = () => {
      resolve(request.result);
    };

    request.onupgradeneeded = () => {
      const db = request.result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME, {
          keyPath: 'id',
          autoIncrement: true
        });
      }
    };
  });
};

// 保存问题数据
export const saveLLMProblem = async (data: any): Promise<number> => {
  try {
    const db = await initDB();
    return new Promise((resolve, reject) => {
      let problemData;

      // 检查传入的数据是否已经是完整的 LLMProblem 格式
      if (data.hasOwnProperty('data') && data.hasOwnProperty('createdAt')) {
        // 如果是完整的记录格式，直接使用
        problemData = {
          isFavorite: false, // 设置默认值
          ...data // 使用传入的所有字段
        };
        console.log('saveLLMProblem: 使用完整记录格式', problemData);
      } else {
        // 如果是原始数据格式，使用旧的逻辑
        const defaultTitle = data.description ?
          (data.description.length > 15 ? data.description.substring(0, 15) + '...' : data.description) :
          '无标题问题';

        const problemCount = Array.isArray(data.problem) ? data.problem.length : 1;
        problemData = {
          data,
          createdAt: new Date(),
          title: defaultTitle,
          isFavorite: false,
          progress: 0, // 默认进度为0
          totalProblems: problemCount, // 根据问题数量设置总数
          completedProblems: new Array(problemCount).fill(false) // 初始化所有问题为未完成
        };
        console.log('saveLLMProblem: 使用原始数据格式', problemData);
      }

      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      const request = store.add(problemData);

      request.onsuccess = () => {
        resolve(request.result as number);
      };

      request.onerror = () => {
        reject('保存问题失败');
      };
    });
  } catch (error) {
    console.error('IndexedDB操作失败:', error);
    throw error;
  }
};

// 获取所有问题
export const getAllProblems = async (): Promise<LLMProblem[]> => {
  try {
    const db = await initDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readonly');
      const store = transaction.objectStore(STORE_NAME);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        reject('获取问题列表失败');
      };
    });
  } catch (error) {
    console.error('IndexedDB操作失败:', error);
    throw error;
  }
};

// 根据ID获取问题
export const getProblemById = async (id: number): Promise<LLMProblem | null> => {
  try {
    const db = await initDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readonly');
      const store = transaction.objectStore(STORE_NAME);
      const request = store.get(id);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        reject('获取问题失败');
      };
    });
  } catch (error) {
    console.error('IndexedDB操作失败:', error);
    throw error;
  }
};

// 更新问题数据
export const updateProblem = async (problem: LLMProblem): Promise<void> => {
  try {
    const db = await initDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      const request = store.put(problem);

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject('更新问题失败');
      };
    });
  } catch (error) {
    console.error('IndexedDB操作失败:', error);
    throw error;
  }
};

// 删除问题
export const deleteProblem = async (id: number): Promise<void> => {
  try {
    const db = await initDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      const request = store.delete(id);

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject('删除问题失败');
      };
    });
  } catch (error) {
    console.error('IndexedDB操作失败:', error);
    throw error;
  }
};

// 临时调试函数：清空所有数据
export const clearAllProblems = async (): Promise<void> => {
  try {
    const db = await initDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([STORE_NAME], 'readwrite');
      const store = transaction.objectStore(STORE_NAME);
      const request = store.clear();

      request.onsuccess = () => {
        console.log('所有数据已清空');
        resolve();
      };

      request.onerror = () => {
        reject('清空数据失败');
      };
    });
  } catch (error) {
    console.error('IndexedDB操作失败:', error);
    throw error;
  }
};

// 切换收藏状态
export const toggleFavorite = async (id: number): Promise<void> => {
  try {
    const problem = await getProblemById(id);
    if (!problem) {
      throw new Error('问题不存在');
    }

    problem.isFavorite = !problem.isFavorite;
    await updateProblem(problem);
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    throw error;
  }
};

// 重命名问题
export const renameProblem = async (id: number, newTitle: string): Promise<void> => {
  try {
    const problem = await getProblemById(id);
    if (!problem) {
      throw new Error('问题不存在');
    }

    problem.title = newTitle;
    await updateProblem(problem);
  } catch (error) {
    console.error('重命名问题失败:', error);
    throw error;
  }
};
